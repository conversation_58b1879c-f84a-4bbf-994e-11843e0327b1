import { TokenPayload } from "@optiwise/dto";
import { AppError } from "@optiwise/utils";
import { NextFunction, Request, RequestHandler, Response } from "express";
import jwt from "jsonwebtoken";

const authenticateToken =
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (secret: string): RequestHandler<any> =>
    async (req: Request, res: Response, next: NextFunction) => {
      const authHeader = req?.headers.authorization;

      const token: string = authHeader?.split(" ")?.[1] || "";

      if (!token) {
        next(new AppError(401, "Invalid or expired Token"));
      }

      try {
        const payload = jwt.verify(token, secret) as TokenPayload;

        if (!payload.id) {
          next(new AppError(401, "Invalid or expired Token"));
        }

        req.user = payload;
        next();
      } catch (error) {
        next(new AppError(401, "Invalid or expired Token"));
      }
    };

export default authenticateToken;
