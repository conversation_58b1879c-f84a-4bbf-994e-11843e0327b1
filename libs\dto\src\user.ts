import { DefaultFields } from "./default";

export enum UserRoles {
  SuperUser = "SuperUser",
  Admin = "Admin",
  User = "User",
}

export interface UserType extends DefaultFields {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  role: UserRoles;
}

export interface UserDto extends Omit<UserType, "password"> {}

export interface CreateUserDto
  extends Omit<UserType, "id" | "created_at" | "updated_at"> {}

export interface UpdateUserDto extends Partial<CreateUserDto> {}

export interface TokenPayload
  extends Pick<
    UserType,
    "id" | "first_name" | "last_name" | "role" | "email"
  > {}
