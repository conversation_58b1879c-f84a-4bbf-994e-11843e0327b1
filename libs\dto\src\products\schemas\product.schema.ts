// libs/dto/src/products/schemas/product.schema.ts
import { z } from "../../utils/zod";

export const ProductCategoryEnum = z.enum(["Product", "Service"]);
export const ItemTypeEnum = z.enum(["buy", "sell", "both"]);

export const ProductSchema = z
  .object({
    id: z.number().int().positive().optional(),
    name: z.string().min(1).max(255),
    itemId: z.string().max(255).nullable().optional(),
    productCategory: ProductCategoryEnum,
    category: z.string().max(255).nullable().optional(),
    itemType: ItemTypeEnum,
    uom: z.string().max(50).nullable().optional(),
    defaultBuyPrice: z
      .number()
      .nonnegative()
      .max(999999999999.99)
      .nullable()
      .optional(),
    defaultSellPrice: z
      .number()
      .nonnegative()
      .max(999999999999.99)
      .nullable()
      .optional(),
    taxPercentage: z.number().nonnegative().max(999.99).nullable().optional(),
    hsnCode: z.string().max(50).nullable().optional(),
    description: z.string().nullable().optional(),
    thumbNailId: z.number().int().positive().nullable().optional(),
    isUsed: z.boolean().default(false),
    requiresInspections: z.boolean().default(false),
    quantityThreshold: z.number().int().default(0),
    quantity: z.number().int().default(0),
    semifinishedQuantity: z.number().int().default(0),
    scrap: z.number().int().default(0),
    additionalFields: z.any().nullable().optional(),
    parentId: z.number().int().positive().nullable().optional(),
    variantIndex: z.number().int().default(0),
    status: z.string().max(50).default("Out of Stock"),
    topParentId: z.number().int().positive().nullable().optional(),
    createdAt: z.date().default(() => new Date()),
    updatedAt: z.date().default(() => new Date()),
  })
  .openapi({ title: "Product" });

export type ProductType = z.infer<typeof ProductSchema>;
