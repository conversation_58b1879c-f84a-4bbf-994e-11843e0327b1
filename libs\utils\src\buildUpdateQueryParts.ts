interface QueryParts {
  updates: string;
  values: unknown[];
  nexParamIndex: number;
}

const buildUpdateQueryParts = (
  data: Record<string, unknown>,
  includeUpdatedAt: boolean = false
): QueryParts => {
  const updates: string[] = [];
  const values: unknown[] = [];
  let index: number = 1;

  const temp = Object.entries(data);
  for (let i = 0; i < temp.length; i++) {
    const [key, value] = temp[i];
    if (value !== undefined) {
      updates.push(`${key} = $${index}`);
      values.push(value);
      index++;
    }
  }

  if (includeUpdatedAt) {
    updates.push(`updated_at = NOW()`);
  }

  return {
    updates: updates?.join(", "),
    values,
    nexParamIndex: index,
  };
};

export default buildUpdateQueryParts;
