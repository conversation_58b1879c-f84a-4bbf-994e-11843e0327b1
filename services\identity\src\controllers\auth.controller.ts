import { Request, Response } from "@optiwise/express";
import as<PERSON><PERSON><PERSON><PERSON> from "express-async-handler";

import { config } from "../config";
import { AuthService } from "../services";

export default class AuthController {
  constructor(private authService: AuthService) {}

  login = asyncHandler(
    async (
      req: Request<unknown, unknown, { email: string; password: string }>,
      res: Response
    ) => {
      const response = await this.authService.login(
        req.body.email,
        req.body.password,
        req.dbPoolKey
      );
      res
        .cookie("refreshToken", response.refreshToken, {
          httpOnly: true,
          maxAge: 1000 * 60 * 60 * 24 * 30,
          secure: config.env === "production",
        })
        .status(200)
        .json({ user: response.user, accessToken: response.accessToken });
    }
  );

  refreshToken = asyncHandler(async (req: Request, res: Response) => {
    const { accessToken } = await this.authService.refreshToken(
      req.cookies.refreshToken,
      req.db<PERSON><PERSON><PERSON><PERSON>
    );
    res.status(201).json({ accessToken });
  });
}
