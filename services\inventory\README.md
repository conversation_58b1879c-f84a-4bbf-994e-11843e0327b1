# Inventory Microservice

This microservice manages product inventory and related operations.  
It is part of the monorepo and can be run either **locally** or inside **Docker**.

---

## 🌍 Environment Variables

The service requires the following environment variables to run:

- **`POSTGRES_HOST`**  
  - Database host.  
  - Use `localhost` when running without Docker.  
  - Use `host.docker.internal` when running inside Docker.

- **`POSTGRES_PORT`**  
  - Port on which PostgreSQL is running (default: `5432`).

- **`POSTGRES_USER`**  
  - Username for authenticating with the PostgreSQL database.

- **`POSTGRES_PASS`**  
  - Password for authenticating with the PostgreSQL database.

- **`POSTGRES_DB`**  
  - The specific database name to connect to within PostgreSQL.

- **`JWT_ACCESS_SECRET`**  
  - Secret key for signing **access tokens** (short-lived, used for authentication).

- **`JWT_REFRESH_SECRET`**  
  - Secret key for signing **refresh tokens** (long-lived, used to renew access tokens).

---

## 📂 Folder Structure

Below is the folder structure with explanations:

```text
inventory/
│
├── config/ # Centralized configuration files
│ ├── db.ts # Database connection config
│ ├── jwt.ts # JWT secrets/config
│ └── ... # Other configs as needed
│
├── controllers/ # Handle HTTP requests/responses
│ └── ProductController.ts
│
├── services/ # Business logic of the application
│ └── ProductService.ts
│
├── routes/ # Express route definitions mapping endpoints to controllers
│ └── productRoutes.ts
│
├── repositories/ # Database query layer
│ └── ProductRepository.ts
│
├── migrations/ # SQL migrations
│ ├── V1__create_products_table.sql
│ └── V2__alter_products_table.sql
```


### Folder Explanations

- **`config/`**  
  Contains configuration files such as database configuration, JWT configuration, and any other configs as needed.

- **`controllers/`**  
  Handle incoming HTTP requests, delegate work to services, and send responses.

- **`services/`**  
  Contain the core business logic of the application.

- **`routes/`**  
  Define the API endpoints and map them to the appropriate controllers.

- **`repositories/`**  
  Handle direct database interactions (queries, inserts, updates, deletes).

- **`migrations/`**  
  Store raw SQL migration files:  
  - `CREATE TABLE` migrations  
  - `ALTER TABLE` migrations  

---
