import { TokenPayload } from "@optiwise/dto";
import { AppError } from "@optiwise/utils";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";

import { config } from "../config";
import { generateAccessToken, generateRefreshToken } from "../config/jwt";
import type { IUserRepository } from "../repositories";

export default class AuthService {
  constructor(private userRepository: IUserRepository) {}

  async login(
    email: string,
    password: string,
    dbPoolKey: string
  ): Promise<{
    accessToken: string;
    refreshToken: string;
    user: TokenPayload;
  }> {
    const user = await this.userRepository.findUserByEmail(email, dbPoolKey);
    if (!user) {
      throw new AppError(401, "Unauthorized, invalid email or password");
    }

    const isAuthenticated = await bcrypt.compare(password, user.password);
    if (!isAuthenticated) {
      throw new AppError(401, "Unauthorized, invalid email or password");
    }

    const payload: TokenPayload = {
      id: user.id,
      email: user?.email,
      first_name: user?.first_name,
      last_name: user.last_name,
      role: user.role,
    };

    const accessToken = generateAccessToken(payload);
    const refreshToken = generateRefreshToken(user.id);

    return { accessToken, refreshToken, user: payload };
  }

  async refreshToken(
    refreshToken: string,
    dbPoolKey: string
  ): Promise<{ accessToken: string }> {
    if (!refreshToken) {
      throw new AppError(
        401,
        "Invalid or expired token, logging out of the system"
      );
    }

    const decoded = jwt.verify(refreshToken, config.jwtRefreshSecret) as {
      id: string;
    };

    if (!decoded.id) {
      throw new AppError(
        401,
        "Invalid or expired token, logging out of the system"
      );
    }

    const user = await this.userRepository.findById(
      +(decoded?.id || 0),
      dbPoolKey
    );
    if (!user) {
      throw new AppError(404, "User not found, logging out of the system");
    }

    const payload: TokenPayload = {
      id: user.id,
      email: user?.email,
      first_name: user?.first_name,
      last_name: user.last_name,
      role: user.role,
    };

    const accessToken = generateAccessToken(payload);
    return { accessToken };
  }
}
