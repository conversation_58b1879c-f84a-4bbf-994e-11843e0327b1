import { Router, authenticateUser } from "@optiwise/express";

import { config } from "../config";
import { userDependency } from "../dependencies";

const router: Router = Router();

router
  .route("/")
  .post(authenticateUser(config.jwtAccessSecret), userDependency.createUser);

router
  .route("/:id")
  .get(authenticateUser(config.jwtAccessSecret), userDependency.getUserById)
  .put(authenticateUser(config.jwtAccessSecret), userDependency.updateUser);

export default router;
