import { CreateProductDTO } from "@optiwise/dto";
import { Request, Response } from "@optiwise/express";
import asyncHandler from "express-async-handler";

import { ProductService } from "../services/ProductService";

export class ProductController {
  constructor(private productService: ProductService) {}

  // Helper to extract tenantId from headers
  private static getTenantId(req: Request): string {
    return (req.headers["x-tenant-id"] as string) ?? "default";
  }

  createProduct = asyncHandler(async (req: Request, res: Response) => {
    const tenantId = ProductController.getTenantId(req);
    const productData: CreateProductDTO = req.body;
    const product = await this.productService.createProduct(
      tenantId,
      productData
    );
    res.status(201).json(product);
  });

  getAllProducts = asyncHandler(async (req: Request, res: Response) => {
    const tenantId = ProductController.getTenantId(req);
    const products = await this.productService.getAllProducts(tenantId);
    res.json(products);
  });

  getProductById = asyncHandler(async (req: Request, res: Response) => {
    const tenantId = ProductController.getTenantId(req);
    const id = parseInt(req.params.id);
    const product = await this.productService.getProductById(tenantId, id);
    res.json(product);
  });

  // Optional update
  // updateProduct = asyncHandler(async (req: Request, res: Response) => {
  //   const tenantId = this.getTenantId(req);
  //   const id = parseInt(req.params.id);
  //   const productData: UpdateProductDTO = req.body;
  //   const product = await this.productService.updateProduct(tenantId, id, productData);
  //   res.json(product);
  // });

  deleteProduct = asyncHandler(async (req: Request, res: Response) => {
    const tenantId = ProductController.getTenantId(req);
    const id = parseInt(req.params.id);
    await this.productService.deleteProduct(tenantId, id);
    res.status(204).send();
  });
}
