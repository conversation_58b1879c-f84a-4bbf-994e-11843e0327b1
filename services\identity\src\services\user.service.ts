import { CreateUserDto, UpdateUserDto, UserDto } from "@optiwise/dto";
import { AppError } from "@optiwise/utils";

import { UserRepository } from "../repositories";

export default class UserService {
  constructor(private userRepository: UserRepository) {}

  async createUser(user: CreateUserDto, dbPoolKey: string): Promise<UserDto> {
    return this.userRepository.create(user, dbPool<PERSON>ey);
  }

  async getUserById(id: number, dbPoolKey: string): Promise<UserDto> {
    const user = await this.userRepository.findById(id, dbPoolKey);
    if (!user) {
      throw new AppError(404, "User not found or deleted");
    }

    return user;
  }

  async updateUser(
    id: number,
    user: UpdateUserDto,
    dbPoolKey: string
  ): Promise<UserDto> {
    await this.getUserById(id, dbPoolKey);

    const updatedUser = await this.userRepository.updateById(
      id,
      user,
      dbPoolKey
    );
    if (!updatedUser) {
      throw new AppError(500, "Failed to update user");
    }

    return updatedUser;
  }
}
