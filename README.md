# Optiwise Backend

This project is a microservice built with **Node.js**, **Express**, and **TypeScript**. It provides a simple architecture for developing microservices that can be easily extended, maintained, and deployed.

## Table of Contents

- [Features](#features)
- [Tech Stack](#tech-stack)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
  - [Running the Application](#running-the-application)
  - [Development Mode](#development-mode)
- [Directory Structure](#directory-structure)
- [Environment Variables](#environment-variables)
- [API Documentation](#api-documentation)

## Features

- REST API built with Express and TypeScript
- Local libraries
- Error handling middleware
- Extensible architecture for adding more microservices
- Support for integration with databases, caching, or other services

## Tech Stack

- **Node.js**: JavaScript runtime for building server-side applications
- **Express.js**: Web framework for building RESTful APIs
- **TypeScript**: A superset of JavaScript that adds static types
- **Jest**: Testing framework
- **Docker**: Containerization
- **Swagger**: API documentation

## Getting Started

### Prerequisites

Before running the project, ensure you have the following installed on your machine:

- [Node.js](https://nodejs.org) (v20 or higher recommended)
- [npm](https://www.npmjs.com/)
- [PostgreSQL](https://www.postgresql.org/)

### Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/AICAN-SD/optiwise-backend.git
   cd optiwise-backend
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Build the local libraries

   ```bash
   npm run build --workspace libs
   ```

### Running the Application

1. **Start the server**:

   ```bash
   npm run start
   ```

2. **Run in Development Mode** (with hot reload):

   ```bash
   npm run dev
   ```

3. **Run in Development Mode (single service)** (with hot reload):
   ```bash
   npm run dev --workspace service/<service-name>
   # eg npm run dev --workspace service/identity
   ```

## Directory Structure

```bash
/libs                           # local reusable libraries
  /<lib-name>                   # library name
    /src                        # source code
    package.json
    tsconfig.json
/services                       # services used in microservice architecture
  /<service-name>               # service name
    /src                        # source code
      /config                   # environment and app configurations (eg: db, config file )
      /controllers              # request handlers and route logic
      /dependencies             # dependencies instantiation
      /migrations               # sql migration scripts
      /repositories             # CRUD sql queries
      /routes                   # express routes definitions
      /services                 # business logic
      app.ts                    # express server app
      index.ts                  # main entry point
    .env                        # environment variables
    package.json
    tsconfig.json
    tsup.config.ts

```

## API Documentation

The API documentation is available through Swagger after running the service locally. You can also generate the documentation manually using a Swagger setup in the code.
