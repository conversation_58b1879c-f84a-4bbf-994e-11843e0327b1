FROM node:20-alpine AS builder
WORKDIR /app

#Copy root tsconfig
COPY tsconfig.json ./

# Copy root workspace files
COPY package*.json ./

# Copy libs + the specific microservice
COPY libs ./libs
COPY services/inventory ./services/inventory

# Install deps for all workspaces
RUN npm install

# Build everything in dependency order
RUN npm run build --workspaces

FROM node:20-alpine AS runtime
WORKDIR /app

COPY services/inventory/dist ./dist

CMD ["node", "dist/index.js"]

# FROM node:20-alpine AS runtime
# WORKDIR /app

# # Copy only built service dist
# COPY --from=builder /app/services/inventory/dist ./dist

# # Copy runtime deps (service’s own)
# COPY services/inventory/package*.json ./
# RUN npm install --omit=dev

# # Optionally copy shared lib dist into node_modules (if needed at runtime)
# COPY --from=builder /app/libs/logger/dist ./node_modules/@optiwise/logger/dist
# COPY --from=builder /app/libs/utils/dist ./node_modules/@optiwise/utils/dist

# CMD ["node", "dist/index.js"]
