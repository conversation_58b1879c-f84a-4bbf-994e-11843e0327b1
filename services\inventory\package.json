{"name": "inventory", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon --exec ts-node ./src/index.ts", "build": "tsup", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "author": "", "license": "ISC", "devDependencies": {"@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.4.0", "@types/pg": "^8.15.5", "@types/swagger-ui-express": "^4.1.8", "ts-node-dev": "^2.0.0"}, "dependencies": {"@asteasolutions/zod-to-openapi": "^8.1.0", "@optiwise/dto": "^1.0.0", "@optiwise/express": "^1.0.0", "@optiwise/logger": "^1.0.0", "@optiwise/middleware": "^1.0.0", "@optiwise/utils": "^1.0.0", "bcryptjs": "^3.0.2", "compression": "^1.8.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "express-async-handler": "^1.2.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "pg": "^8.16.3", "swagger-ui-express": "^5.0.1"}}