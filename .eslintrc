{"env": {"node": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "airbnb-base", "airbnb-typescript/base", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 12, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "prettier"], "rules": {"no-console": "error", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "error", "no-plusplus": "off", "no-await-in-loop": "off", "no-underscore-dangle": "off", "func-names": "off", "no-param-reassign": "off", "import/prefer-default-export": "off", "radix": "off", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "prettier/prettier": ["error", {"endOfLine": "auto"}], "indent": "off", "@typescript-eslint/indent": "off"}}