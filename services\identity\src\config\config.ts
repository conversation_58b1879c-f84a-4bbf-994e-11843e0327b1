import dotenv from "dotenv";

dotenv.config();

const config = {
  env: process.env.NODE_ENV || "production",
  port: process.env.PORT || 5002,
  jwtAccessSecret: process.env.JWT_ACCESS_SECRET || "",
  jwtRefreshSecret: process.env.JWT_REFRESH_SECRET || "",
  postgres: {
    host: process.env.POSTGRES_HOST || "",
    port: +(process.env.POSTGRES_PORT || 5432),
    user: process?.env?.POSTGRES_USER || "",
    password: process?.env?.POSTGRES_PASS || "",
    database: process?.env?.POSTGRES_DB || "",
  },
};

export default config;
