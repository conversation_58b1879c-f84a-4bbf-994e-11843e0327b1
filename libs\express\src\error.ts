/* eslint-disable @typescript-eslint/no-unused-vars */
import { AppError } from "@optiwise/utils";
import { NextFunction, Request, Response } from "express";

export const notFound = (req: Request, res: Response, next: NextFunction) => {
  const error = new Error(`Not found - ${req.originalUrl}`);
  res.status(404);
  next(error);
};

export const errorHandler = (
  err: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const error =
    err instanceof AppError
      ? err
      : new AppError(res?.statusCode || 500, err?.message);
  const sc = error?.statusCode || res?.statusCode;
  const statusCode = sc === 200 ? 500 : sc;

  res.status(statusCode).json({
    status: statusCode,
    message: err.message,
    stack: process.env.NODE_ENV === "production" ? "" : err.stack,
  });
};
