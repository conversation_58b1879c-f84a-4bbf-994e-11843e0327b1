import { NextFunction, Request, RequestHandler, Response } from "express";
import { Pool } from "pg";

export type DbFactory = Record<string, Pool>;

const populateDbPool =
  (dbFactory: DbFactory, serviceName: string): RequestHandler =>
  (req: Request, res: Response, next: NextFunction) => {
    const tenantId: string = req.headers["X-Tenant-Id"] as string;
    if (tenantId) {
      req.dbPoolKey = tenantId;
      if (!dbFactory[tenantId]) {
        dbFactory[tenantId] = new Pool({
          host: process.env.POSTGRES_HOST,
          user: process.env.POSTGRES_USER,
          password: process.env.POSTGRES_PASS,
          port: +(process.env.POSTGRES_PORT || 5432),
          database: `${tenantId}-${serviceName}`,
        });
      }
    } else {
      req.dbPoolKey = "shared";
    }

    next();
  };

export default populateDbPool;
