/* eslint-disable @typescript-eslint/no-namespace */
import { TokenPayload } from "@optiwise/dto";
import express from "express";

declare global {
  namespace Express {
    interface Request {
      user?: TokenPayload;
      dbPoolKey: string;
    }
  }
}

export {
  ErrorRequestHandler,
  Express,
  NextFunction,
  Response,
  Request,
  RequestHandler,
  RequestParamHandler,
  Router,
  RouterOptions,
  Handler,
  Send,
  IRoute,
  Locals,
  Errback,
  IRouter,
  MediaType,
  Application,
  CookieOptions,
  IRouterHandler,
  IRouterMatcher,
} from "express";
export { default as createExpressServer } from "./app";
export { default as authenticateUser } from "./middleware/authenticateUser";
export {
  default as populateDbPool,
  DbFactory,
} from "./middleware/populateDbPool";

export default express;
