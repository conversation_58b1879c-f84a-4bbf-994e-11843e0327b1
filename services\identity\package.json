{"name": "identity", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node ./src/index.ts", "build": "tsup", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@optiwise/dto": "^1.0.0", "@optiwise/express": "^1.0.0", "@optiwise/logger": "^1.0.0", "@optiwise/middleware": "^1.0.0", "@optiwise/utils": "^1.0.0", "bcryptjs": "^3.0.2", "dotenv": "^17.2.2", "express": "^5.1.0", "express-async-handler": "^1.2.0", "jsonwebtoken": "^9.0.2", "pg": "^8.16.3"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.9", "@types/pg": "^8.15.5", "tsup": "^8.5.0"}}