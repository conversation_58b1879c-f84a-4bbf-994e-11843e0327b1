import type { TokenPayload, UserRoles, UserType } from "@optiwise/dto";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";

import type { IUserRepository } from "../repositories";
import AuthService from "../services/auth.service";

jest.mock("bcryptjs", () => ({ compare: jest.fn() }));
jest.mock("../config/jwt", () => ({
  generateAccessToken: jest.fn(),
  generateRefreshToken: jest.fn(),
}));
jest.mock("jsonwebtoken", () => ({ verify: jest.fn() }));

const { generateAccessToken, generateRefreshToken } = jest.requireMock(
  "../config/jwt"
) as {
  generateAccessToken: jest.Mock;
  generateRefreshToken: jest.Mock;
};

describe("AuthService", () => {
  const tenant = "tenant1";
  const user: UserType = {
    id: 1,
    email: "<EMAIL>",
    password: "hashed",
    first_name: "<PERSON>",
    last_name: "<PERSON><PERSON>",
    phone_number: "",
    role: "User" as UserRoles,
    created_at: new Date() as unknown as Date,
    updated_at: new Date() as unknown as Date,
  };

  const repoMock = (): jest.Mocked<IUserRepository> => ({
    create: jest.fn(),
    updateById: jest.fn(),
    find: jest.fn(),
    findById: jest.fn(),
    deleteById: jest.fn(),
    findUserByEmail: jest.fn(),
  });

  beforeEach(() => {
    jest.resetAllMocks();
  });

  it("login throws 401 when user not found", async () => {
    const repo = repoMock();
    repo.findUserByEmail.mockResolvedValue(null);
    const service = new AuthService(repo);
    await expect(
      service.login("<EMAIL>", "pass", tenant)
    ).rejects.toMatchObject({
      statusCode: 401,
    });
  });

  it("login throws 401 when password mismatch", async () => {
    (bcrypt.compare as jest.Mock).mockResolvedValue(false);
    const repo = repoMock();
    repo.findUserByEmail.mockResolvedValue(user);
    const service = new AuthService(repo);
    await expect(
      service.login(user.email, "wrong", tenant)
    ).rejects.toMatchObject({
      statusCode: 401,
    });
  });

  it("login returns tokens and payload on success", async () => {
    (bcrypt.compare as jest.Mock).mockResolvedValue(true);
    generateAccessToken.mockReturnValue("access");
    generateRefreshToken.mockReturnValue("refresh");

    const repo = repoMock();
    repo.findUserByEmail.mockResolvedValue(user);
    const service = new AuthService(repo);
    const result = await service.login(user.email, "pass", tenant);

    expect(result.accessToken).toBe("access");
    expect(result.refreshToken).toBe("refresh");
    const assertTokenPayload = (_: TokenPayload) => {};
    assertTokenPayload(result.user);
    expect(result.user).toEqual(
      expect.objectContaining({
        id: user.id,
        email: user.email,
      })
    );
  });

  it("refreshToken throws 401 when missing", async () => {
    const repo = repoMock();
    const service = new AuthService(repo);
    await expect(service.refreshToken("", tenant)).rejects.toMatchObject({
      statusCode: 401,
    });
  });

  it("refreshToken throws 401 when invalid", async () => {
    (jwt.verify as jest.Mock).mockImplementation(() => ({ id: undefined }));
    const repo = repoMock();
    const service = new AuthService(repo);
    await expect(service.refreshToken("bad", tenant)).rejects.toMatchObject({
      statusCode: 401,
    });
  });

  it("refreshToken throws 404 when user not found", async () => {
    (jwt.verify as jest.Mock).mockImplementation(() => ({ id: "1" }));
    const repo = repoMock();
    repo.findById.mockResolvedValue(null);
    const service = new AuthService(repo);
    await expect(service.refreshToken("good", tenant)).rejects.toMatchObject({
      statusCode: 404,
    });
  });

  it("refreshToken returns new access token", async () => {
    (jwt.verify as jest.Mock).mockImplementation(() => ({ id: "1" }));
    generateAccessToken.mockReturnValue("newAccess");

    const repo = repoMock();
    repo.findById.mockResolvedValue({
      id: user.id,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      phone_number: user.phone_number,
      role: user.role,
      created_at: user.created_at as unknown as Date,
      updated_at: user.updated_at as unknown as Date,
    });
    const service = new AuthService(repo);
    const res = await service.refreshToken("good", tenant);
    expect(res.accessToken).toBe("newAccess");
  });
});
