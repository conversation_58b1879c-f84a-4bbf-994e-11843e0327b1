{"name": "optiwise-backend", "version": "0.0.0", "description": "", "workspaces": ["libs/*", "services/*", "api-gateway"], "scripts": {"build": "npm run build --workspaces", "dev": "npm run dev --workspaces", "pretty": "prettier --write \"**/*.{js,jsx,ts,tsx,json}\"", "lint": "eslint . --ext ts,js --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint --fix .", "prepare": "husky", "test": "jest --runInBand", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "devDependencies": {"@swc/core": "^1.13.5", "@swc/jest": "^0.2.39", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/jest": "^30.0.0", "@types/node": "^24.4.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "husky": "^9.1.7", "jest": "^30.1.3", "lint-staged": "^16.1.6", "nodemon": "^3.1.9", "prettier": "^3.5.2", "ts-node": "^10.9.2", "tsup": "^8.5.0", "typescript": "~5.4.4"}}