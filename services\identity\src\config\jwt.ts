import { TokenPayload } from "@optiwise/dto";
import jwt from "jsonwebtoken";

import config from "./config";

export const generateAccessToken = (user: TokenPayload) => {
  const token = jwt.sign(user, config.jwtAccessSecret, {
    expiresIn: "5m",
  });

  return token;
};

export const generateRefreshToken = (id: number) => {
  const token = jwt.sign({ id }, config.jwtRefreshSecret, {
    expiresIn: "30d",
  });

  return token;
};
