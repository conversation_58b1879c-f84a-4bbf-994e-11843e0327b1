{"name": "@optiwise/express", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p tsconfig.json"}, "dependencies": {"@optiwise/dto": "^1.0.0", "@optiwise/logger": "^1.0.0", "@optiwise/utils": "1.0.0", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "^5.1.0", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.16.3"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/morgan": "^1.9.9"}, "private": true}