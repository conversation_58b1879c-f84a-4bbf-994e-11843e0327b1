import { CreateUserDto, UpdateUserDto, UserDto, UserType } from "@optiwise/dto";
import { DbFactory } from "@optiwise/express";
import { buildUpdateQueryParts } from "@optiwise/utils";
import bcrypt from "bcryptjs";
import { Pool } from "pg";

export interface IUserRepository {
  create(user: CreateUserDto, dbPoolKey: string): Promise<UserDto>;
  updateById(
    id: number,
    user: UpdateUserDto,
    dbPoolKey: string
  ): Promise<UserDto | null>;
  find(dbPoolKey: string): Promise<UserDto[]>;
  findById(id: number, dbPoolKey: string): Promise<UserDto | null>;
  deleteById(id: number, dbPoolKey: string): Promise<boolean>;
  findUserByEmail(email: string, dbPoolKey: string): Promise<UserType | null>;
}

export default class UserRepository implements IUserRepository {
  constructor(private dbFactory: DbFactory) {}

  async create(user: CreateUserDto, dbP<PERSON>Key: string): Promise<UserDto> {
    const pool: Pool = this.dbFactory[dbPoolKey];
    const query = `
            INSERT INTO users (email, password, first_name, last_name, role, phone_number, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
            RETURNING id, first_name, last_name, email, role, phone_number, created_at, updated_at
        `;

    const hash = await bcrypt.hash(user.password, 10);

    const values = [
      user.email,
      hash,
      user.first_name,
      user.last_name,
      user.role,
      user.phone_number,
    ];

    const result = await pool.query(query, values);

    return result.rows[0];
  }

  async updateById(
    id: number,
    user: UpdateUserDto,
    dbPoolKey: string
  ): Promise<UserDto | null> {
    const pool: Pool = this.dbFactory[dbPoolKey];
    if (user?.password && user?.password?.length > 0) {
      const hash = await bcrypt.hash(user.password, 10);
      user.password = hash;
    }

    const { updates, values, nexParamIndex } = buildUpdateQueryParts(
      user,
      true
    );

    values.push(id);

    const query = `
            UPDATE users (email, password, first_name, last_name, role, phone_number, created_at, updated_at)
            SET ${updates}
            WHERE id = $${nexParamIndex}
            RETURNING id, first_name, last_name, email, role, phone_number, created_at, updated_at
    `;

    const result = await pool.query(query, values);
    return result.rows[0] || null;
  }

  async find(dbPoolKey: string): Promise<UserDto[]> {
    const pool: Pool = this.dbFactory[dbPoolKey];
    const query = `
            SELECT id, first_name, last_name, email, role, phone_number, created_at, updated_at FROM users
            ORDER BY created_at DESC
    `;
    const result = await pool.query(query);
    return result.rows;
  }

  async findById(id: number, dbPoolKey: string): Promise<UserDto | null> {
    const pool: Pool = this.dbFactory[dbPoolKey];
    const query = `
            SELECT id, first_name, last_name, email, role, phone_number, created_at, updated_at FROM users
            WHERE id = $1
    `;
    const result = await pool.query(query, [id]);
    return result.rows[0] || null;
  }

  async deleteById(id: number, dbPoolKey: string): Promise<boolean> {
    const pool: Pool = this.dbFactory[dbPoolKey];
    const query = `
            DELETE FROM users
            WHERE id = $1 
            RETURNING id
    `;
    const result = await pool.query(query, [id]);
    return (result.rowCount ?? 0) > 0;
  }

  async findUserByEmail(
    email: string,
    dbPoolKey: string
  ): Promise<UserType | null> {
    const pool: Pool = this.dbFactory[dbPoolKey];
    const query = `
            SELECT * FROM users
            WHERE email = $1 
    `;
    const result = await pool.query(query, [email]);
    return result.rows[0] || null;
  }
}
