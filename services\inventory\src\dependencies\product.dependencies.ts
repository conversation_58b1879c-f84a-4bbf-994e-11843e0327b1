import { productRepoFactory } from "../config/productRepoFactory";
import { ProductController } from "../controllers/ProductController";
import { ProductService } from "../services/ProductService";

// factory in repositories folder

// instantiate service
const productService = new ProductService(productRepoFactory);

// instantiate controller
export const productController = new ProductController(productService);
