import { Pool } from "pg";

import tenantConfigJson from "./tenantConfig.json";

const tenantConfig = tenantConfigJson as Record<string, string>;

// generic interface for repo factories
export type RepoFactory<T> = (tenantId: string) => T;

const pools: Record<string, Pool> = {};

export function getTenantPool(tenantId: string): Pool {
  const dbName = tenantConfig[tenantId] ?? "inventory";
  if (!dbName) {
    throw new Error(`No database mapping found for tenantId: ${tenantId}`);
  }

  if (!pools[tenantId]) {
    pools[tenantId] = new Pool({
      host: process.env.POSTGRES_HOST ?? "localhost",
      port: Number(process.env.POSTGRES_PORT ?? 5432),
      user: process.env.POSTGRES_USER ?? "postgres",
      password: process.env.POSTGRES_PASS ?? "password",
      database: dbName,
    });
  }

  return pools[tenantId];
}

export async function closeAllPools() {
  await Promise.all(Object.values(pools).map((pool) => pool.end()));
}
