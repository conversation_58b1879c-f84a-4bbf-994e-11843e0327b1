// libs/dto/src/products/schemas/listProducts.schema.ts
import { z } from "../../utils/zod";
import { ProductSchema } from "./product.schema";

// --- Query Schema ---
export const ListProductsQuerySchema = z
  .object({
    page: z.number().int().min(1).default(1),
    limit: z.number().int().min(1).max(100).default(10),
    search: z.string().optional(), // full-text search
    category: z.string().optional(),
    itemType: z.string().optional(),
    status: z.string().optional(),
    sortBy: z.string().optional(), // e.g., "name", "createdAt"
    sortOrder: z.enum(["asc", "desc"]).optional().default("asc"),
  })
  .openapi({ title: "ListProductsQuery" });

export type ListProductsQueryType = z.infer<typeof ListProductsQuerySchema>;

// --- Response Schema ---
export const PaginatedProductsSchema = z
  .object({
    data: z.array(ProductSchema),
    total: z.number().int().nonnegative(),
    page: z.number().int().min(1),
    limit: z.number().int().min(1),
    pages: z.number().int().nonnegative(), // total pages
  })
  .openapi({ title: "PaginatedProducts" });

export type PaginatedProductsType = z.infer<typeof PaginatedProductsSchema>;
