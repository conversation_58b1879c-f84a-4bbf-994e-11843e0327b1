import type { CreateProductDTO, Product } from "@optiwise/dto";

import type { IProductRepository } from "../repositories/IProductRepository";
import { ProductService } from "../services/ProductService";

describe("ProductService", () => {
  const tenant = "tenant";

  const product: Product = {
    id: 1,
    name: "Item",
    description: "desc",
    sku: "sku",
    price: 10,
    quantity: 10,
    created_at: new Date() as unknown as Date,
    updated_at: new Date() as unknown as Date,
  } as Product;

  const repoMock = (): jest.Mocked<IProductRepository> => ({
    create: jest.fn(
      async (p: CreateProductDTO) => ({ ...product, ...p }) as Product
    ),
    findAll: jest.fn(async () => [product]),
    findById: jest.fn(async (id: number) => (id === 1 ? product : null)),
    delete: jest.fn(async (id: number) => id === 1),
  });

  const factory = (r: jest.Mocked<IProductRepository>) => (_id: string) => r;

  it("createProduct returns created product", async () => {
    const repo = repoMock();
    const service = new ProductService(factory(repo));
    const created = await service.createProduct(tenant, {
      name: "New",
      description: "desc",
      sku: "sku",
      price: 20,
      quantity: 10,
    });
    expect(created.name).toBe("New");
  });

  it("getAllProducts returns list", async () => {
    const repo = repoMock();
    const service = new ProductService(factory(repo));
    const list = await service.getAllProducts(tenant);
    expect(Array.isArray(list)).toBe(true);
    expect(repo.findAll).toHaveBeenCalled();
  });

  it("getProductById returns product when found", async () => {
    const repo = repoMock();
    const service = new ProductService(factory(repo));
    const res = await service.getProductById(tenant, 1);
    expect(res.id).toBe(1);
  });

  it("getProductById throws 404 when not found", async () => {
    const repo = repoMock();
    const service = new ProductService(factory(repo));
    await expect(service.getProductById(tenant, 999)).rejects.toMatchObject({
      statusCode: 404,
    });
  });

  it("deleteProduct succeeds when repo returns true", async () => {
    const repo = repoMock();
    const service = new ProductService(factory(repo));
    await expect(service.deleteProduct(tenant, 1)).resolves.toBeUndefined();
  });

  it("deleteProduct throws 404 when repo returns false", async () => {
    const repo = repoMock();
    const service = new ProductService(factory(repo));
    await expect(service.deleteProduct(tenant, 123)).rejects.toMatchObject({
      statusCode: 404,
    });
  });
});
