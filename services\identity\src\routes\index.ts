import { Router, populateDbPool } from "@optiwise/express";

import { config } from "../config";
import dbFactory from "../config/db";
import authRouter from "./auth.route";
import userRouter from "./user.route";

const router: Router = Router();

const routes: { path: string; route: Router }[] = [
  {
    path: "/user",
    route: userRouter,
  },
  {
    path: "/auth",
    route: authRouter,
  },
];

router.use(populateDbPool(dbFactory, config.postgres.database));

routes?.forEach((route) => {
  router.use(route.path, route.route);
});

export default router;
