"use strict";

module.exports = {
  testEnvironment: "node",
  transform: {
    "^.+\\.(t|j)s$": [
      "@swc/jest",
      {
        sourceMaps: true,
        module: { type: "commonjs" },
        jsc: {
          target: "es2020",
          parser: { syntax: "typescript" },
        },
      },
    ],
  },
  moduleFileExtensions: ["ts", "js", "json", "node"],
  testMatch: ["<rootDir>/src/**/__tests__/**/*.test.ts"], // Look for tests under src/__tests__ with *.test.ts
  clearMocks: true, // Resets mock calls and instances before each test
  setupFilesAfterEnv: ["<rootDir>/../../jest.setup.ts"], // Runs before each test
  moduleNameMapper: {
    "^@optiwise/(.+)$": "<rootDir>/../../libs/$1/src", // eg: Maps @optiwise/dto to libs/dto/src
  },
  coverageDirectory: "<rootDir>/coverage", // Where to store coverage reports
  collectCoverageFrom: [
    "<rootDir>/src/**/*.ts",
    "!<rootDir>/src/**/__tests__/**",
    "!**/node_modules/**",
    "!**/dist/**",
  ],
};
