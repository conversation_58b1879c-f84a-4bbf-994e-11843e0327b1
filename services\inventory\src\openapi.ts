// optiwise-backend/services/inventory/src/openapi.ts
import { OpenApiGeneratorV3 } from "@asteasolutions/zod-to-openapi";
import { registry } from "@optiwise/dto/dist/utils/openapiRegistry";
import { registerAllPaths } from "@optiwise/dto/dist/utils/registerAllPaths";

import config from "./config/config";

registerAllPaths();

const generator = new OpenApiGeneratorV3(registry.definitions);

// Generate the full document
export const openApiDocument = generator.generateDocument({
  openapi: "3.0.0",
  info: {
    title: "Inventory Service API",
    version: "1.0.0",
    description: "API documentation for the Inventory Service",
  },
  servers: [
    {
      url: `http://localhost:${config.port}/api/inventory`,
      description: "Optiwise Inventory Service (local)",
    },
  ],
  security: [{ bearerAuth: [] }],
  tags: [
    {
      name: "Products",
      description: "Operations related to products",
    },
  ],
});
