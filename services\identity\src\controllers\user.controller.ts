import { CreateUserDto, UpdateUserDto } from "@optiwise/dto";
import { Request, Response } from "@optiwise/express";
import asyncHandler from "express-async-handler";

import { UserService } from "../services";

export default class UserController {
  constructor(private userService: UserService) {}

  createUser = asyncHandler(
    async (req: Request<unknown, unknown, CreateUserDto>, res: Response) => {
      const user = await this.userService.createUser(req.body, req.dbPoolKey);
      res.status(201).json(user);
    }
  );

  getUserById = asyncHandler(
    async (req: Request<{ id: number }>, res: Response) => {
      const user = await this.userService.getUserById(
        +req.params.id,
        req.dbPoolKey
      );
      res.status(200).json(user);
    }
  );

  updateUser = asyncHandler(
    async (
      req: Request<{ id: number }, unknown, UpdateUserDto>,
      res: Response
    ) => {
      const user = await this.userService.updateUser(
        +req.params.id,
        req.body,
        req.dbPoolKey
      );
      res.status(200).json(user);
    }
  );
}
