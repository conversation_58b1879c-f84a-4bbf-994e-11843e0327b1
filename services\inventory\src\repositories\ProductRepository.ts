import { CreateProductDTO, Product } from "@optiwise/dto";
import { Pool } from "pg";

import { IProductRepository } from "./IProductRepository";

export class ProductRepository implements IProductRepository {
  constructor(private pool: Pool) {}

  async create(product: CreateProductDTO): Promise<Product> {
    const query = `
            INSERT INTO products (name, description, sku, price, quantity, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
            RETURNING *
        `;
    const values = [
      product.name,
      product.description,
      product.sku,
      product.price,
      product.quantity,
    ];
    const result = await this.pool.query(query, values);
    return result.rows[0];
  }

  async findAll(): Promise<Product[]> {
    const query = "SELECT * FROM products ORDER BY created_at DESC";
    const result = await this.pool.query(query);
    return result.rows;
  }

  async findById(id: number): Promise<Product | null> {
    const query = "SELECT * FROM products WHERE id = $1";
    const result = await this.pool.query(query, [id]);
    return result.rows[0] || null;
  }

  // async update(id: number, product: UpdateProductDTO): Promise<Product | null> {
  //   const updates: string[] = [];
  //   const values: any[] = [];
  //   let paramIndex = 1;

  //   // Dynamically build update query based on provided fields
  //   Object.entries(product).forEach(([key, value]) => {
  //     if (value !== undefined) {
  //       updates.push(`${key} = $${paramIndex}`);
  //       values.push(value);
  //       paramIndex++;
  //     }
  //   });

  //   if (updates.length === 0) return null;

  //   values.push(id);
  //   const query = `
  //           UPDATE products
  //           SET ${updates.join(", ")}, updated_at = NOW()
  //           WHERE id = $${paramIndex}
  //           RETURNING *
  //       `;

  //   const result = await this.pool.query(query, values);
  //   return result.rows[0] || null;
  // }

  async delete(id: number): Promise<boolean> {
    const query = "DELETE FROM products WHERE id = $1 RETURNING id";
    const result = await this.pool.query(query, [id]);
    return (result.rowCount ?? 0) > 0;
  }
}
