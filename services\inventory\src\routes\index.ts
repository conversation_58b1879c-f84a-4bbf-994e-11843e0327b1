import { Router } from "@optiwise/express";
import swaggerUi from "swagger-ui-express";

import { openApiDocument } from "../openapi";
import productRouter from "./ProductRoutes";

const router: Router = Router();

router.use("/docs", swaggerUi.serve, swaggerUi.setup(openApiDocument));

const routes: { path: string; route: Router }[] = [
  {
    path: "/products",
    route: productRouter,
  },
];

routes?.forEach((route) => {
  router.use(route.path, route.route);
});

export default router;
