import { CreateProductDTO, Product } from "@optiwise/dto";
import { AppError, RepoFactory } from "@optiwise/utils";

import { IProductRepository } from "../repositories/IProductRepository";

export class ProductService {
  constructor(private repoFactory: RepoFactory<IProductRepository>) {}

  async createProduct(
    tenantId: string,
    productData: CreateProductDTO
  ): Promise<Product> {
    const productRepository = this.repoFactory(tenantId);
    return productRepository.create(productData);
  }

  async getAllProducts(tenantId: string): Promise<Product[]> {
    const productRepository = this.repoFactory(tenantId);
    return productRepository.findAll();
  }

  async getProductById(tenantId: string, id: number): Promise<Product> {
    const productRepository = this.repoFactory(tenantId);
    const product = await productRepository.findById(id);
    if (!product) {
      throw new AppError(404, `Product with id ${id} not found`);
    }
    return product;
  }

  // Optional: Update method
  // async updateProduct(tenantId: string, id: number, productData: UpdateProductDTO): Promise<Product> {
  //   const productRepository = this.repoFactory(tenantId);
  //   const existingProduct = await productRepository.findById(id);
  //   if (!existingProduct) throw new AppError(`Product with id ${id} not found`, 404);
  //   const updatedProduct = await productRepository.update(id, productData);
  //   if (!updatedProduct) throw new AppError("Failed to update product", 500);
  //   return updatedProduct;
  // }

  async deleteProduct(tenantId: string, id: number): Promise<void> {
    const productRepository = this.repoFactory(tenantId);
    const deleted = await productRepository.delete(id);
    if (!deleted) {
      throw new AppError(404, `Product with id ${id} not found`);
    }
  }
}
