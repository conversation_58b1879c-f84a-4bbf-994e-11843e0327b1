// optiwise-backend/libs/express/src/app.ts
import compression from "compression";
import cookieParser from "cookie-parser";
import cors from "cors";
import express, { Express, Request, Response } from "express";
import helmet from "helmet";

import { errorHandler, notFound } from "./error";
import * as morgan from "./morgan";

const createExpressServer = (
  path: string,
  routes: express.Router,
  sizeLimit: string = "50mb"
): Express => {
  const app: Express = express();

  app.use(morgan.successHandler);
  app.use(morgan.errorHandler);

  // set security HTTP headers
  app.use(
    helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-eval'", "data:"],
          connectSrc: ["'self'", "data:"],
          imgSrc: ["'self'", "data:"],
          objectSrc: ["'none'"],
          baseUri: ["'self'"],
        },
      },
    })
  );

  // parse json request body
  app.use(express.json({ limit: sizeLimit }));

  // parse urlencoded request body
  app.use(express.urlencoded({ limit: sizeLimit, extended: true }));

  // parse cookie request
  app.use(cookieParser());

  // gzip compression
  app.use(compression());

  // enable cors
  app.use(cors());

  app.use(path, routes);

  app.get("/", (_req: Request, res: Response) => {
    res.send("API server is active");
  });

  app.use(notFound);
  app.use(errorHandler);

  return app;
};
export default createExpressServer;
